<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>💖 真正的3D爱心表白 💖</title>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/three.js/r128/three.min.js"></script>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            background: linear-gradient(45deg, #0a0a0a, #1a1a2e, #16213e);
            overflow: hidden;
            font-family: 'Arial', sans-serif;
            height: 100vh;
        }

        #canvas-container {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        /* 表白文字样式 */
        .confession-text {
            position: absolute;
            bottom: 15%;
            left: 50%;
            transform: translateX(-50%);
            color: #fff;
            text-align: center;
            font-size: 28px;
            font-weight: bold;
            text-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
            z-index: 100;
            animation: textGlow 2s ease-in-out infinite alternate;
        }

        @keyframes textGlow {
            from {
                text-shadow: 0 0 20px rgba(255, 107, 107, 0.8);
                transform: translateX(-50%) scale(1);
            }
            to {
                text-shadow: 0 0 30px rgba(255, 23, 68, 1), 0 0 40px rgba(255, 107, 107, 0.8);
                transform: translateX(-50%) scale(1.05);
            }
        }

        .click-hint {
            position: absolute;
            bottom: 5%;
            left: 50%;
            transform: translateX(-50%);
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            z-index: 100;
            animation: pulse 2s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.6; }
            50% { opacity: 1; }
        }


    </style>
</head>
<body>
    <!-- 3D渲染容器 -->
    <div id="canvas-container"></div>

    <!-- 表白文字 -->
    <div class="confession-text">
        💖 我好喜欢你 💖<br>
        <span style="font-size: 20px; margin-top: 10px; display: block;">
            这是真正的3D爱心为你跳动
        </span>
    </div>

    <!-- 点击提示 -->
    <div class="click-hint">
        点击屏幕释放3D爱心粒子 ✨
    </div>

    <script>
        // Three.js 3D场景设置
        let scene, camera, renderer, heart, particles = [];
        let mouseX = 0, mouseY = 0;

        // 初始化3D场景
        function init() {
            // 创建场景
            scene = new THREE.Scene();

            // 创建相机
            camera = new THREE.PerspectiveCamera(75, window.innerWidth / window.innerHeight, 0.1, 1000);
            camera.position.z = 5;

            // 创建渲染器
            renderer = new THREE.WebGLRenderer({ antialias: true, alpha: true });
            renderer.setSize(window.innerWidth, window.innerHeight);
            renderer.setClearColor(0x000000, 0);
            document.getElementById('canvas-container').appendChild(renderer.domElement);

            // 创建真正的3D爱心
            createHeart();

            // 创建粒子系统
            createParticleSystem();

            // 添加光照
            const ambientLight = new THREE.AmbientLight(0xffffff, 0.6);
            scene.add(ambientLight);

            const pointLight = new THREE.PointLight(0xff1744, 1, 100);
            pointLight.position.set(0, 0, 10);
            scene.add(pointLight);

            // 开始渲染循环
            animate();
        }

        // 创建真正的3D爱心几何体 - 使用标准爱心数学公式
        function createHeart() {
            // 使用标准爱心参数方程：
            // x = 16*sin³(t)
            // y = 13*cos(t) - 5*cos(2*t) - 2*cos(3*t) - cos(4*t)

            const heartShape = new THREE.Shape();
            const scale = 0.05; // 缩放因子

            // 生成爱心形状的点
            let firstPoint = true;
            for (let t = 0; t <= Math.PI * 2; t += 0.1) {
                const x = 16 * Math.pow(Math.sin(t), 3) * scale;
                const y = (13 * Math.cos(t) - 5 * Math.cos(2 * t) - 2 * Math.cos(3 * t) - Math.cos(4 * t)) * scale;

                if (firstPoint) {
                    heartShape.moveTo(x, y);
                    firstPoint = false;
                } else {
                    heartShape.lineTo(x, y);
                }
            }
            heartShape.closePath();

            // 挤出几何体创建3D效果
            const extrudeSettings = {
                depth: 1.0,
                bevelEnabled: true,
                bevelSegments: 8,
                steps: 2,
                bevelSize: 0.15,
                bevelThickness: 0.15
            };

            const geometry = new THREE.ExtrudeGeometry(heartShape, extrudeSettings);

            // 创建发光材质效果
            const material = new THREE.MeshPhongMaterial({
                color: 0xff1744,
                shininess: 100,
                transparent: true,
                opacity: 0.95,
                emissive: 0x440022 // 自发光效果
            });

            heart = new THREE.Mesh(geometry, material);
            heart.position.set(0, 0, 0);
            heart.scale.set(2, 2, 2);
            scene.add(heart);
        }

        // 创建3D粒子系统
        function createParticleSystem() {
            const particleCount = 100;
            const geometry = new THREE.BufferGeometry();
            const positions = new Float32Array(particleCount * 3);
            const colors = new Float32Array(particleCount * 3);

            for (let i = 0; i < particleCount; i++) {
                positions[i * 3] = (Math.random() - 0.5) * 20;
                positions[i * 3 + 1] = (Math.random() - 0.5) * 20;
                positions[i * 3 + 2] = (Math.random() - 0.5) * 20;

                colors[i * 3] = 1;     // R
                colors[i * 3 + 1] = 0.1; // G
                colors[i * 3 + 2] = 0.3; // B
            }

            geometry.setAttribute('position', new THREE.BufferAttribute(positions, 3));
            geometry.setAttribute('color', new THREE.BufferAttribute(colors, 3));

            const material = new THREE.PointsMaterial({
                size: 0.1,
                vertexColors: true,
                transparent: true,
                opacity: 0.8
            });

            const particleSystem = new THREE.Points(geometry, material);
            scene.add(particleSystem);
            particles.push(particleSystem);
        }

        // 动画循环
        function animate() {
            requestAnimationFrame(animate);

            // 爱心跳动和旋转
            if (heart) {
                const time = Date.now() * 0.001;
                heart.rotation.y += 0.01;
                heart.rotation.x = Math.sin(time * 2) * 0.1;

                // 跳动效果
                const scale = 0.3 + Math.sin(time * 3) * 0.05;
                heart.scale.set(scale, scale, scale);
            }

            // 粒子动画
            particles.forEach(particle => {
                particle.rotation.y += 0.005;
                particle.rotation.x += 0.002;
            });

            // 相机跟随鼠标
            camera.position.x += (mouseX * 0.001 - camera.position.x) * 0.05;
            camera.position.y += (-mouseY * 0.001 - camera.position.y) * 0.05;
            camera.lookAt(scene.position);

            renderer.render(scene, camera);
        }

        // 鼠标移动事件
        document.addEventListener('mousemove', (event) => {
            mouseX = event.clientX - window.innerWidth / 2;
            mouseY = event.clientY - window.innerHeight / 2;
        });

        // 点击创建新的3D爱心
        document.addEventListener('click', (event) => {
            createFloatingHeart(event.clientX, event.clientY);
        });

        // 创建漂浮的小爱心
        function createFloatingHeart(x, y) {
            // 使用相同的爱心形状
            const heartShape = new THREE.Shape();
            const scale = 0.3;

            heartShape.moveTo(0, -1.5 * scale);

            // 左半边心形
            heartShape.bezierCurveTo(-2 * scale, -3 * scale, -4 * scale, -1.5 * scale, -2 * scale, 0);
            heartShape.bezierCurveTo(-2 * scale, 1 * scale, -1 * scale, 1.5 * scale, 0, 2 * scale);

            // 右半边心形
            heartShape.bezierCurveTo(1 * scale, 1.5 * scale, 2 * scale, 1 * scale, 2 * scale, 0);
            heartShape.bezierCurveTo(4 * scale, -1.5 * scale, 2 * scale, -3 * scale, 0, -1.5 * scale);

            const geometry = new THREE.ExtrudeGeometry(heartShape, {
                depth: 0.3,
                bevelEnabled: true,
                bevelSegments: 4,
                steps: 1,
                bevelSize: 0.05,
                bevelThickness: 0.05
            });

            // 随机粉红色系
            const colors = [0xff1744, 0xff6b9d, 0xffc1cc, 0xff8a95, 0xffb3ba];
            const material = new THREE.MeshPhongMaterial({
                color: colors[Math.floor(Math.random() * colors.length)],
                transparent: true,
                opacity: 0.9,
                emissive: 0x220011
            });

            const miniHeart = new THREE.Mesh(geometry, material);

            // 将屏幕坐标转换为3D坐标
            const vector = new THREE.Vector3(
                (x / window.innerWidth) * 2 - 1,
                -(y / window.innerHeight) * 2 + 1,
                0.5
            );
            vector.unproject(camera);

            miniHeart.position.copy(vector);
            miniHeart.scale.set(0.15, 0.15, 0.15);
            miniHeart.rotation.x = Math.PI; // 翻转显示
            scene.add(miniHeart);

            // 动画效果
            const startTime = Date.now();
            function animateMiniHeart() {
                const elapsed = Date.now() - startTime;
                if (elapsed < 4000) {
                    miniHeart.position.y += 0.015;
                    miniHeart.rotation.y += 0.08;
                    miniHeart.rotation.z += 0.05;
                    miniHeart.material.opacity = 0.9 - (elapsed / 4000) * 0.9;
                    requestAnimationFrame(animateMiniHeart);
                } else {
                    scene.remove(miniHeart);
                }
            }
            animateMiniHeart();
        }

        // 窗口大小调整
        window.addEventListener('resize', () => {
            camera.aspect = window.innerWidth / window.innerHeight;
            camera.updateProjectionMatrix();
            renderer.setSize(window.innerWidth, window.innerHeight);
        });

        // 页面加载完成后初始化
        window.addEventListener('load', () => {
            init();
            setTimeout(() => {
                alert('💖 这是真正的3D爱心！用鼠标移动查看不同角度，点击创建更多3D爱心！');
            }, 1000);
        });
    </script>
</body>
</html>
