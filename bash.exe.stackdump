Stack trace:
Frame         Function      Args
0007FFFFA910  00021005FE8E (000210285F68, 00021026AB6E, 0007FFFFA910, 0007FFFF9810) msys-2.0.dll+0x1FE8E
0007FFFFA910  0002100467F9 (000000000000, 000000000000, 000000000000, 0007FFFFABE8) msys-2.0.dll+0x67F9
0007FFFFA910  000210046832 (000210286019, 0007FFFFA7C8, 0007FFFFA910, 000000000000) msys-2.0.dll+0x6832
0007FFFFA910  000210068CF6 (000000000000, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28CF6
0007FFFFA910  000210068E24 (0007FFFFA920, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x28E24
0007FFFFABF0  00021006A225 (0007FFFFA920, 000000000000, 000000000000, 000000000000) msys-2.0.dll+0x2A225
End of stack trace
Loaded modules:
000100400000 bash.exe
7FF8F4AF0000 ntdll.dll
7FF8F3C20000 KERNEL32.DLL
7FF8F2560000 KERNELBASE.dll
7FF8F2B60000 USER32.dll
7FF8F2980000 win32u.dll
7FF8F3D70000 GDI32.dll
7FF8F2070000 gdi32full.dll
7FF8F22A0000 msvcp_win.dll
7FF8F2190000 ucrtbase.dll
000210040000 msys-2.0.dll
7FF8F3400000 advapi32.dll
7FF8F3DA0000 msvcrt.dll
7FF8F2FD0000 sechost.dll
7FF8F2950000 bcrypt.dll
7FF8F3E50000 RPCRT4.dll
7FF8F17F0000 CRYPTBASE.DLL
7FF8F28D0000 bcryptPrimitives.dll
7FF8F3F70000 IMM32.DLL
